# 技术选型

## 后端技术栈

### 核心框架
- **FastAPI** - 高性能异步Web框架
- **Python 3.11+** - 主要开发语言
- **asyncio** - 异步处理

### 数据库
- **SQLite** - 主数据库，简单部署，适合单机环境
- **Qdrant** - 向量数据库，高性能语义搜索

### 数据库相关
- **SQLAlchemy** - ORM框架
- **Alembic** - 数据库迁移工具
- **aiosqlite** - 异步SQLite驱动

### 其他组件
- **WebSocket** - 实时通信
- **pydantic** - 数据验证和序列化
- **aiofiles** - 异步文件操作
- **python-multipart** - 文件上传支持

## 前端技术栈

### 核心框架
- **React 18** - 用户界面框架
- **Vite** - 构建工具和开发服务器
- **TypeScript** - 类型安全的JavaScript

### 状态管理和路由
- **Zustand** - 轻量级状态管理
- **React Router** - 路由管理

### UI和样式
- **Tailwind CSS** - 原子化CSS框架
- **Headless UI** - 无样式组件库
- **Framer Motion** - 动画库

### 通信和工具
- **WebSocket API** - 实时通信
- **Axios** - HTTP客户端
- **React Query** - 数据获取和缓存

## 开发工具

### 包管理
- **Poetry** - Python依赖管理
- **pnpm** - 前端包管理

### 代码质量
- **Black** - Python代码格式化
- **isort** - Python导入排序
- **ESLint** - JavaScript/TypeScript代码检查
- **Prettier** - 前端代码格式化

### 开发环境
- **Git** - 版本控制
- **VS Code** - 推荐IDE

## AI集成

### 模型接入
- **OpenAI API** - 主要AI能力提供
- **本地模型支持** - 备选方案（Ollama等）
- **向量化模型** - 文本嵌入生成

## 部署环境

### 运行环境
- **Python 3.11+** 运行时
- **Node.js 18+** 前端构建
- **Qdrant服务** 独立部署

### 系统要求
- **操作系统** - Windows/Linux/macOS
- **内存** - 最低4GB，推荐8GB+
- **存储** - SSD推荐，支持快速数据访问
- **网络** - 局域网环境，无需外网依赖
