# UI/UX设计规范

## 设计理念

### 核心原则
- **现代简洁**：采用现代设计语言，界面简洁优雅
- **莫兰迪色系**：使用柔和的莫兰迪色彩，营造舒适的视觉体验
- **一致性**：保持视觉和交互的一致性
- **响应性**：快速响应用户操作
- **可访问性**：支持不同能力的用户使用

### 设计目标
- 让用户专注于与Silicon的对话
- 提供清晰的系统状态反馈
- 支持高效的多任务操作
- 适配多种设备和屏幕尺寸
- 营造温和、专业的交互氛围

## 视觉设计系统

### 色彩规范

#### 莫兰迪主色调
```css
/* 浅色主题 - 莫兰迪色系 */
--primary-50: #f8f9fa    /* 极浅灰蓝 */
--primary-100: #e9ecef   /* 浅灰蓝 */
--primary-200: #dee2e6   /* 中浅灰蓝 */
--primary-300: #ced4da   /* 中灰蓝 */
--primary-400: #adb5bd   /* 中深灰蓝 */
--primary-500: #6c757d   /* 主色 - 莫兰迪灰 */
--primary-600: #495057   /* 深灰蓝 */
--primary-700: #343a40   /* 更深灰蓝 */
--primary-800: #212529   /* 极深灰蓝 */

/* 深色主题 - 莫兰迪色系 */
--primary-dark-50: #2d3748   /* 深色背景 */
--primary-dark-100: #4a5568  /* 深灰蓝 */
--primary-dark-200: #718096  /* 中深灰蓝 */
--primary-dark-300: #a0aec0  /* 中灰蓝 */
--primary-dark-400: #cbd5e0  /* 中浅灰蓝 */
--primary-dark-500: #e2e8f0  /* 主色 - 浅灰蓝 */
--primary-dark-600: #edf2f7  /* 更浅灰蓝 */
--primary-dark-700: #f7fafc  /* 极浅灰蓝 */
```

#### 莫兰迪语义色彩
```css
/* 功能色彩 - 莫兰迪风格 */
--success: #8fbc8f    /* 成功状态 - 柔和绿 */
--warning: #deb887    /* 警告状态 - 柔和黄 */
--error: #cd919e      /* 错误状态 - 柔和红 */
--info: #87ceeb       /* 信息提示 - 柔和蓝 */

/* 文本色彩 - 莫兰迪风格 */
--text-primary: #2d3748    /* 深灰蓝 */
--text-secondary: #4a5568  /* 中深灰蓝 */
--text-muted: #718096      /* 中灰蓝 */
--text-accent: #6c757d     /* 强调色 */

/* 背景色彩 - 莫兰迪风格 */
--bg-primary: #f8f9fa      /* 主背景 - 极浅灰蓝 */
--bg-secondary: #e9ecef    /* 次背景 - 浅灰蓝 */
--bg-tertiary: #dee2e6     /* 三级背景 - 中浅灰蓝 */
--bg-accent: #f1f3f4       /* 强调背景 */
```

### 字体规范

#### 字体族
```css
/* 主要字体 */
--font-sans: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif
--font-mono: 'JetBrains Mono', 'Fira Code', monospace

/* 中文字体 */
--font-zh: 'PingFang SC', 'Microsoft YaHei', sans-serif
```

#### 字体大小
```css
--text-xs: 0.75rem    /* 12px */
--text-sm: 0.875rem   /* 14px */
--text-base: 1rem     /* 16px */
--text-lg: 1.125rem   /* 18px */
--text-xl: 1.25rem    /* 20px */
--text-2xl: 1.5rem    /* 24px */
--text-3xl: 1.875rem  /* 30px */
```

#### 字重
```css
--font-light: 300
--font-normal: 400
--font-medium: 500
--font-semibold: 600
--font-bold: 700
```

### 间距系统

#### 基础间距
```css
--space-1: 0.25rem    /* 4px */
--space-2: 0.5rem     /* 8px */
--space-3: 0.75rem    /* 12px */
--space-4: 1rem       /* 16px */
--space-5: 1.25rem    /* 20px */
--space-6: 1.5rem     /* 24px */
--space-8: 2rem       /* 32px */
--space-10: 2.5rem    /* 40px */
--space-12: 3rem      /* 48px */
```

#### 组件间距
- **消息间距**：16px
- **面板间距**：24px
- **按钮内边距**：12px 24px
- **输入框内边距**：12px 16px

### 圆角和阴影

#### 圆角规范
```css
--radius-sm: 0.25rem   /* 4px */
--radius-md: 0.375rem  /* 6px */
--radius-lg: 0.5rem    /* 8px */
--radius-xl: 0.75rem   /* 12px */
--radius-full: 9999px  /* 完全圆角 */
```

#### 阴影系统
```css
--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05)
--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1)
--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1)
--shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1)
```

## 组件设计规范

### 按钮设计

#### 按钮类型
- **主要按钮**：重要操作，使用主色调
- **次要按钮**：一般操作，使用边框样式
- **文本按钮**：轻量操作，仅文本样式
- **图标按钮**：空间受限时使用

#### 按钮状态 - 莫兰迪风格
```css
/* 默认状态 */
.btn-primary {
  background: var(--primary-500);
  color: var(--bg-primary);
  border: 1px solid var(--primary-400);
  border-radius: var(--radius-md);
  padding: var(--space-3) var(--space-6);
  transition: all 0.2s ease;
}

/* 悬停状态 */
.btn-primary:hover {
  background: var(--primary-600);
  border-color: var(--primary-500);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 激活状态 */
.btn-primary:active {
  background: var(--primary-700);
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* 禁用状态 */
.btn-primary:disabled {
  background: var(--primary-200);
  color: var(--text-muted);
  border-color: var(--primary-200);
  cursor: not-allowed;
  transform: none;
}
```

### 输入框设计

#### 输入框样式 - 莫兰迪风格
```css
.input-field {
  border: 1px solid var(--primary-300);
  border-radius: var(--radius-md);
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-base);
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.input-field:focus {
  border-color: var(--primary-500);
  outline: none;
  box-shadow: 0 0 0 3px rgba(108, 117, 125, 0.1);
  background: var(--bg-accent);
}

.input-field::placeholder {
  color: var(--text-muted);
}
```

#### 输入框状态
- **默认状态**：灰色边框
- **聚焦状态**：主色边框 + 阴影
- **错误状态**：红色边框 + 错误提示
- **禁用状态**：灰色背景 + 禁用光标

### 消息气泡设计

#### 用户消息 - 莫兰迪风格
```css
.message-user {
  background: var(--primary-500);
  color: var(--bg-primary);
  border: 1px solid var(--primary-400);
  border-radius: var(--radius-lg) var(--radius-lg) var(--radius-sm) var(--radius-lg);
  padding: var(--space-3) var(--space-4);
  margin-left: var(--space-12);
  align-self: flex-end;
  box-shadow: var(--shadow-sm);
}
```

#### Silicon消息 - 莫兰迪风格
```css
.message-silicon {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--primary-200);
  border-radius: var(--radius-lg) var(--radius-lg) var(--radius-lg) var(--radius-sm);
  padding: var(--space-3) var(--space-4);
  margin-right: var(--space-12);
  align-self: flex-start;
  box-shadow: var(--shadow-sm);
}
```

## 交互设计规范

### 动画和过渡

#### 基础动画
```css
/* 标准过渡时间 */
--transition-fast: 0.15s
--transition-normal: 0.2s
--transition-slow: 0.3s

/* 缓动函数 */
--ease-in-out: cubic-bezier(0.4, 0, 0.2, 1)
--ease-out: cubic-bezier(0, 0, 0.2, 1)
--ease-in: cubic-bezier(0.4, 0, 1, 1)
```

#### 常用动画
- **淡入淡出**：透明度变化
- **滑动**：位置变化
- **缩放**：大小变化
- **弹性**：回弹效果

### 加载状态

#### 加载指示器
- **骨架屏**：内容加载时显示
- **旋转器**：操作进行中显示
- **进度条**：有明确进度的操作
- **脉冲动画**：等待状态指示

#### 流式文本动画
```css
@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

.streaming-text {
  animation: typing 0.5s steps(40, end);
  border-right: 2px solid var(--primary-500);
}
```

### 反馈机制

#### 状态反馈
- **成功操作**：绿色提示 + 成功图标
- **错误操作**：红色提示 + 错误图标
- **警告信息**：黄色提示 + 警告图标
- **信息提示**：蓝色提示 + 信息图标

#### 操作反馈
- **按钮点击**：轻微缩放 + 阴影变化
- **链接悬停**：颜色变化 + 下划线
- **拖拽操作**：透明度变化 + 阴影

## 响应式设计

### 断点系统
```css
/* 移动设备 */
@media (max-width: 640px) { /* sm */ }

/* 平板设备 */
@media (max-width: 768px) { /* md */ }

/* 小型桌面 */
@media (max-width: 1024px) { /* lg */ }

/* 大型桌面 */
@media (max-width: 1280px) { /* xl */ }
```

### 布局适配

#### 桌面端布局
- 左侧主功能区：60-70%宽度
- 右侧辅助功能区：30-40%宽度（固定显示）
- 最小宽度：1024px
- 右侧栏通过标签页切换不同功能面板

#### 平板端布局
- 右侧栏保持显示，但可调整宽度
- 触摸友好的按钮大小
- 适当增大间距
- 标签页切换优化为触摸操作

#### 移动端布局
- 双屏滑动设计
- 底部导航点
- 全屏输入模式

## 可访问性规范

### 键盘导航
- 所有交互元素支持Tab导航
- 明确的焦点指示器
- 合理的Tab顺序
- 快捷键支持

### 屏幕阅读器
- 语义化HTML结构
- 适当的ARIA标签
- 图片的alt文本
- 表单的label关联

### 色彩对比
- 文本对比度至少4.5:1
- 大文本对比度至少3:1
- 非文本元素对比度至少3:1
- 提供高对比度模式

### 字体和间距
- 最小字体大小16px
- 行高至少1.5倍
- 充足的点击区域（44px最小）
- 支持字体缩放
