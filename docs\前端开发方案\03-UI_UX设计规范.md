# UI/UX设计规范

## 设计理念

### 核心原则
- **简洁直观**：界面简洁，操作直观易懂
- **一致性**：保持视觉和交互的一致性
- **响应性**：快速响应用户操作
- **可访问性**：支持不同能力的用户使用

### 设计目标
- 让用户专注于与Silicon的对话
- 提供清晰的系统状态反馈
- 支持高效的多任务操作
- 适配多种设备和屏幕尺寸

## 视觉设计系统

### 色彩规范

#### 主色调
```css
/* 浅色主题 */
--primary-50: #f0f9ff
--primary-100: #e0f2fe
--primary-500: #0ea5e9  /* 主色 */
--primary-600: #0284c7
--primary-900: #0c4a6e

/* 深色主题 */
--primary-dark-50: #0f172a
--primary-dark-100: #1e293b
--primary-dark-500: #3b82f6  /* 主色 */
--primary-dark-600: #2563eb
--primary-dark-900: #1e40af
```

#### 语义色彩
```css
/* 功能色彩 */
--success: #10b981    /* 成功状态 */
--warning: #f59e0b    /* 警告状态 */
--error: #ef4444      /* 错误状态 */
--info: #3b82f6       /* 信息提示 */

/* 文本色彩 */
--text-primary: #111827
--text-secondary: #6b7280
--text-muted: #9ca3af

/* 背景色彩 */
--bg-primary: #ffffff
--bg-secondary: #f9fafb
--bg-tertiary: #f3f4f6
```

### 字体规范

#### 字体族
```css
/* 主要字体 */
--font-sans: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif
--font-mono: 'JetBrains Mono', 'Fira Code', monospace

/* 中文字体 */
--font-zh: 'PingFang SC', 'Microsoft YaHei', sans-serif
```

#### 字体大小
```css
--text-xs: 0.75rem    /* 12px */
--text-sm: 0.875rem   /* 14px */
--text-base: 1rem     /* 16px */
--text-lg: 1.125rem   /* 18px */
--text-xl: 1.25rem    /* 20px */
--text-2xl: 1.5rem    /* 24px */
--text-3xl: 1.875rem  /* 30px */
```

#### 字重
```css
--font-light: 300
--font-normal: 400
--font-medium: 500
--font-semibold: 600
--font-bold: 700
```

### 间距系统

#### 基础间距
```css
--space-1: 0.25rem    /* 4px */
--space-2: 0.5rem     /* 8px */
--space-3: 0.75rem    /* 12px */
--space-4: 1rem       /* 16px */
--space-5: 1.25rem    /* 20px */
--space-6: 1.5rem     /* 24px */
--space-8: 2rem       /* 32px */
--space-10: 2.5rem    /* 40px */
--space-12: 3rem      /* 48px */
```

#### 组件间距
- **消息间距**：16px
- **面板间距**：24px
- **按钮内边距**：12px 24px
- **输入框内边距**：12px 16px

### 圆角和阴影

#### 圆角规范
```css
--radius-sm: 0.25rem   /* 4px */
--radius-md: 0.375rem  /* 6px */
--radius-lg: 0.5rem    /* 8px */
--radius-xl: 0.75rem   /* 12px */
--radius-full: 9999px  /* 完全圆角 */
```

#### 阴影系统
```css
--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05)
--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1)
--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1)
--shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1)
```

## 组件设计规范

### 按钮设计

#### 按钮类型
- **主要按钮**：重要操作，使用主色调
- **次要按钮**：一般操作，使用边框样式
- **文本按钮**：轻量操作，仅文本样式
- **图标按钮**：空间受限时使用

#### 按钮状态
```css
/* 默认状态 */
.btn-primary {
  background: var(--primary-500);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: var(--space-3) var(--space-6);
}

/* 悬停状态 */
.btn-primary:hover {
  background: var(--primary-600);
  transform: translateY(-1px);
}

/* 激活状态 */
.btn-primary:active {
  background: var(--primary-700);
  transform: translateY(0);
}

/* 禁用状态 */
.btn-primary:disabled {
  background: var(--text-muted);
  cursor: not-allowed;
}
```

### 输入框设计

#### 输入框样式
```css
.input-field {
  border: 1px solid var(--text-muted);
  border-radius: var(--radius-md);
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-base);
  transition: border-color 0.2s ease;
}

.input-field:focus {
  border-color: var(--primary-500);
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
```

#### 输入框状态
- **默认状态**：灰色边框
- **聚焦状态**：主色边框 + 阴影
- **错误状态**：红色边框 + 错误提示
- **禁用状态**：灰色背景 + 禁用光标

### 消息气泡设计

#### 用户消息
```css
.message-user {
  background: var(--primary-500);
  color: white;
  border-radius: var(--radius-lg) var(--radius-lg) var(--radius-sm) var(--radius-lg);
  padding: var(--space-3) var(--space-4);
  margin-left: var(--space-12);
  align-self: flex-end;
}
```

#### Silicon消息
```css
.message-silicon {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border-radius: var(--radius-lg) var(--radius-lg) var(--radius-lg) var(--radius-sm);
  padding: var(--space-3) var(--space-4);
  margin-right: var(--space-12);
  align-self: flex-start;
}
```

## 交互设计规范

### 动画和过渡

#### 基础动画
```css
/* 标准过渡时间 */
--transition-fast: 0.15s
--transition-normal: 0.2s
--transition-slow: 0.3s

/* 缓动函数 */
--ease-in-out: cubic-bezier(0.4, 0, 0.2, 1)
--ease-out: cubic-bezier(0, 0, 0.2, 1)
--ease-in: cubic-bezier(0.4, 0, 1, 1)
```

#### 常用动画
- **淡入淡出**：透明度变化
- **滑动**：位置变化
- **缩放**：大小变化
- **弹性**：回弹效果

### 加载状态

#### 加载指示器
- **骨架屏**：内容加载时显示
- **旋转器**：操作进行中显示
- **进度条**：有明确进度的操作
- **脉冲动画**：等待状态指示

#### 流式文本动画
```css
@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

.streaming-text {
  animation: typing 0.5s steps(40, end);
  border-right: 2px solid var(--primary-500);
}
```

### 反馈机制

#### 状态反馈
- **成功操作**：绿色提示 + 成功图标
- **错误操作**：红色提示 + 错误图标
- **警告信息**：黄色提示 + 警告图标
- **信息提示**：蓝色提示 + 信息图标

#### 操作反馈
- **按钮点击**：轻微缩放 + 阴影变化
- **链接悬停**：颜色变化 + 下划线
- **拖拽操作**：透明度变化 + 阴影

## 响应式设计

### 断点系统
```css
/* 移动设备 */
@media (max-width: 640px) { /* sm */ }

/* 平板设备 */
@media (max-width: 768px) { /* md */ }

/* 小型桌面 */
@media (max-width: 1024px) { /* lg */ }

/* 大型桌面 */
@media (max-width: 1280px) { /* xl */ }
```

### 布局适配

#### 桌面端布局
- 左侧主功能区：60-70%宽度
- 右侧辅助功能区：30-40%宽度
- 最小宽度：1024px

#### 平板端布局
- 可折叠的右侧栏
- 触摸友好的按钮大小
- 适当增大间距

#### 移动端布局
- 双屏滑动设计
- 底部导航点
- 全屏输入模式

## 可访问性规范

### 键盘导航
- 所有交互元素支持Tab导航
- 明确的焦点指示器
- 合理的Tab顺序
- 快捷键支持

### 屏幕阅读器
- 语义化HTML结构
- 适当的ARIA标签
- 图片的alt文本
- 表单的label关联

### 色彩对比
- 文本对比度至少4.5:1
- 大文本对比度至少3:1
- 非文本元素对比度至少3:1
- 提供高对比度模式

### 字体和间距
- 最小字体大小16px
- 行高至少1.5倍
- 充足的点击区域（44px最小）
- 支持字体缩放
