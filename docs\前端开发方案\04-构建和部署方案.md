# 构建和部署方案

## 开发环境配置

### 环境要求
- **Node.js**：18.0+ LTS版本
- **pnpm**：8.0+ 包管理器
- **Git**：版本控制
- **VS Code**：推荐IDE

### 项目初始化
```bash
# 创建React项目
pnpm create vite silicon-frontend --template react-ts

# 安装依赖
cd silicon-frontend
pnpm install

# 安装开发工具
pnpm add -D @types/node @vitejs/plugin-react
pnpm add -D eslint @typescript-eslint/parser @typescript-eslint/eslint-plugin
pnpm add -D prettier eslint-config-prettier eslint-plugin-prettier
pnpm add -D tailwindcss postcss autoprefixer
pnpm add -D @testing-library/react @testing-library/jest-dom vitest
```

### 核心依赖
```json
{
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.8.0",
    "zustand": "^4.3.0",
    "@tanstack/react-query": "^4.24.0",
    "axios": "^1.3.0",
    "framer-motion": "^10.0.0",
    "react-hook-form": "^7.43.0",
    "react-dropzone": "^14.2.0",
    "react-intersection-observer": "^9.4.0"
  }
}
```

## 构建配置

### Vite配置
```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  
  // 路径别名
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components'),
      '@pages': path.resolve(__dirname, './src/pages'),
      '@hooks': path.resolve(__dirname, './src/hooks'),
      '@utils': path.resolve(__dirname, './src/utils'),
      '@types': path.resolve(__dirname, './src/types'),
      '@stores': path.resolve(__dirname, './src/stores'),
    },
  },
  
  // 开发服务器配置
  server: {
    port: 3000,
    host: '0.0.0.0', // 允许局域网访问
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
      },
      '/ws': {
        target: 'ws://localhost:8000',
        ws: true,
      },
    },
  },
  
  // 构建配置
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          ui: ['framer-motion'],
        },
      },
    },
  },
  
  // 环境变量前缀
  envPrefix: 'SILICON_',
})
```

### TypeScript配置
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@components/*": ["./src/components/*"],
      "@pages/*": ["./src/pages/*"],
      "@hooks/*": ["./src/hooks/*"],
      "@utils/*": ["./src/utils/*"],
      "@types/*": ["./src/types/*"],
      "@stores/*": ["./src/stores/*"]
    }
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

### ESLint配置
```json
{
  "extends": [
    "eslint:recommended",
    "@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "prettier"
  ],
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint", "react", "react-hooks"],
  "rules": {
    "react/react-in-jsx-scope": "off",
    "react/prop-types": "off",
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/explicit-function-return-type": "off",
    "prefer-const": "error",
    "no-var": "error"
  },
  "settings": {
    "react": {
      "version": "detect"
    }
  }
}
```

### Prettier配置
```json
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5",
  "printWidth": 80,
  "bracketSpacing": true,
  "arrowParens": "avoid"
}
```

## 项目结构

### 目录组织
```
src/
├── components/          # 通用组件
│   ├── ui/             # 基础UI组件
│   ├── layout/         # 布局组件
│   └── common/         # 公共组件
├── pages/              # 页面组件
├── hooks/              # 自定义Hooks
├── stores/             # 状态管理
├── utils/              # 工具函数
├── types/              # TypeScript类型定义
├── styles/             # 样式文件
├── assets/             # 静态资源
├── locales/            # 国际化文件
└── tests/              # 测试文件
```

### 文件命名规范
- **组件文件**：PascalCase（如 `MessageList.tsx`）
- **Hook文件**：camelCase，use前缀（如 `useWebSocket.ts`）
- **工具文件**：camelCase（如 `formatDate.ts`）
- **类型文件**：camelCase（如 `messageTypes.ts`）
- **样式文件**：kebab-case（如 `message-list.css`）

## 环境管理

### 环境变量配置
```bash
# .env.development
SILICON_API_URL=http://localhost:8000
SILICON_WS_URL=ws://localhost:8000/ws
SILICON_ENV=development

# .env.production
SILICON_API_URL=http://silicon-host:8000
SILICON_WS_URL=ws://silicon-host:8000/ws
SILICON_ENV=production
```

### 环境类型定义
```typescript
// src/types/env.ts
interface ImportMetaEnv {
  readonly SILICON_API_URL: string
  readonly SILICON_WS_URL: string
  readonly SILICON_ENV: 'development' | 'production'
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
```

## 构建脚本

### package.json脚本
```json
{
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "lint:fix": "eslint src --ext ts,tsx --fix",
    "format": "prettier --write \"src/**/*.{ts,tsx,css,md}\"",
    "type-check": "tsc --noEmit",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest --coverage"
  }
}
```

### 构建优化
```typescript
// 代码分割配置
const optimizeChunks = {
  cacheGroups: {
    vendor: {
      test: /[\\/]node_modules[\\/]/,
      name: 'vendors',
      chunks: 'all',
    },
    common: {
      name: 'common',
      minChunks: 2,
      chunks: 'all',
      enforce: true,
    },
  },
}

// 资源压缩
const compressionConfig = {
  algorithm: 'gzip',
  test: /\.(js|css|html|svg)$/,
  threshold: 8192,
  minRatio: 0.8,
}
```

## 部署方案

### 静态文件部署
```bash
# 构建生产版本
pnpm run build

# 部署到Silicon主机的静态文件目录
cp -r dist/* /path/to/silicon/static/

# 或使用nginx服务静态文件
nginx -s reload
```

### 开发环境部署
```bash
# 启动开发服务器
pnpm run dev

# 局域网访问
# 前端：http://[silicon-host]:3000
# 后端：http://[silicon-host]:8000
```

### 生产环境部署
```bash
#!/bin/bash
# deploy.sh

# 构建前端
echo "Building frontend..."
pnpm run build

# 检查构建结果
if [ ! -d "dist" ]; then
  echo "Build failed!"
  exit 1
fi

# 备份旧版本
if [ -d "/opt/silicon/frontend" ]; then
  mv /opt/silicon/frontend /opt/silicon/frontend.backup.$(date +%Y%m%d_%H%M%S)
fi

# 部署新版本
cp -r dist /opt/silicon/frontend

# 重启服务（如果使用nginx）
systemctl reload nginx

echo "Frontend deployed successfully!"
```

## 性能优化

### 代码分割
```typescript
// 路由级别的代码分割
import { lazy, Suspense } from 'react'

const ChatPage = lazy(() => import('@pages/ChatPage'))
const SettingsPage = lazy(() => import('@pages/SettingsPage'))

// 使用Suspense包装
<Suspense fallback={<LoadingSpinner />}>
  <ChatPage />
</Suspense>
```

### 资源优化
```typescript
// 图片懒加载
import { useIntersectionObserver } from '@hooks/useIntersectionObserver'

const LazyImage = ({ src, alt }: { src: string; alt: string }) => {
  const [ref, isVisible] = useIntersectionObserver()
  
  return (
    <div ref={ref}>
      {isVisible && <img src={src} alt={alt} />}
    </div>
  )
}
```

### 缓存策略
```typescript
// Service Worker缓存
const CACHE_NAME = 'silicon-frontend-v1'
const urlsToCache = [
  '/',
  '/static/css/main.css',
  '/static/js/main.js',
]

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  )
})
```

## 监控和调试

### 错误监控
```typescript
// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error)
  // 可以发送到错误监控服务
})

// React错误边界
class ErrorBoundary extends Component {
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('React error:', error, errorInfo)
  }
}
```

### 性能监控
```typescript
// 性能指标收集
const observer = new PerformanceObserver((list) => {
  for (const entry of list.getEntries()) {
    console.log('Performance:', entry.name, entry.duration)
  }
})

observer.observe({ entryTypes: ['measure', 'navigation'] })
```
