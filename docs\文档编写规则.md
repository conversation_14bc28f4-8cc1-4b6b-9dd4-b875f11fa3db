# Silicon项目文档编写规则

## 文档结构原则

### 目录组织
- `/docs/` - 全局性内容，项目级别的重要文档
- `/docs/项目设计/` - 具体的设计文档，按模块分类
- 其他子目录 - 根据需要创建，如实现记录、测试文档等

### 设计文档编写原则

1. **简明扼要**
   - 使用简洁的语言表达核心概念
   - 避免冗长的描述和解释
   - 重点突出关键信息

2. **避免代码**
   - 设计文档不包含大段代码
   - 可以包含简单的伪代码或流程图
   - 重点描述逻辑和架构，而非实现细节

3. **模块化组织**
   - 每个模块或功能部分独立成文档
   - 使用统一的编号系统
   - 文档间保持逻辑关联

4. **标准格式**
   - 每个模块文档包含：功能描述、设计要素、接口定义、依赖关系
   - 使用统一的标题层级和格式
   - 保持文档结构的一致性

## 文档命名规范

- 总体规划：`00-总体规划.md`
- 模块文档：`01-模块名称.md`, `02-模块名称.md` 等
- 使用中文命名，便于理解和查找

## 更新维护

- 设计变更时及时更新相关文档
- 保持文档与实际实现的同步
- 定期回顾和优化文档结构
