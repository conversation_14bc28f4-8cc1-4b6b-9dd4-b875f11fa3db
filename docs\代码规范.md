# Silicon项目代码规范

## 核心设计原则

### 极致的模块化设计
- 各部分可单独开发，充分解耦
- 模块间通过明确的接口进行通信
- 避免循环依赖，保持清晰的依赖关系
- 支持模块的独立测试和部署

### 单一职责原则
- 每个模块、类、函数都有明确的单一职责
- 一个类只负责一个业务领域
- 一个函数只实现一个具体功能
- 避免"万能"类和函数

### 代码可读性
- 代码应该像文档一样易读
- 优先选择清晰的表达而非简洁的技巧
- 复杂逻辑必须添加注释说明
- 使用有意义的变量和函数名

## 编码标准

### 文件组织
- 尽可能将每个代码文件控制在200行以内
- 超过200行的文件应考虑拆分
- 相关功能的文件放在同一目录下
- 使用清晰的目录结构反映代码架构

### 命名规范
- 变量、函数、类名要能表达其用途和功能
- 使用完整的英文单词，避免缩写
- 布尔变量使用is/has/can等前缀
- 常量使用全大写字母和下划线

### 函数设计
- 每个函数只实现一个功能，避免过于复杂
- 函数参数不超过5个，超过时使用对象传参
- 函数长度控制在30行以内
- 纯函数优于有副作用的函数

## 语言特定规范

### Python规范
- 遵循PEP 8编码标准
- 使用类型提示（Type Hints）
- 文档字符串使用Google风格
- 异常处理要具体明确

### TypeScript/React规范
- 使用严格的TypeScript配置
- 组件使用函数式组件和Hooks
- Props和State必须定义类型
- 使用ESLint和Prettier保持代码风格

## 注释和文档

### 注释原则
- 解释"为什么"而不是"是什么"
- 复杂算法必须有详细注释
- 业务逻辑的关键决策点要说明
- 临时解决方案要标注TODO

### 文档要求
- 每个模块都有README说明
- 公共API必须有完整文档
- 重要的设计决策要记录
- 保持文档与代码同步更新

## 错误处理

### 异常处理
- 使用具体的异常类型
- 异常信息要清晰描述问题
- 关键操作要有错误恢复机制
- 记录详细的错误日志

### 边界条件
- 输入验证要全面
- 处理空值和边界情况
- 网络请求要有超时和重试
- 资源使用要有限制和清理

## 性能考虑

### 资源管理
- 及时释放不需要的资源
- 避免内存泄漏
- 数据库连接要正确关闭
- 文件操作要使用上下文管理器

### 算法效率
- 选择合适的数据结构
- 避免不必要的循环嵌套
- 大数据量处理要考虑分页
- 缓存频繁访问的数据

## 安全规范

### 输入安全
- 所有用户输入都要验证
- 防止SQL注入和XSS攻击
- 文件上传要检查类型和大小
- 敏感信息不能硬编码

### 数据保护
- 敏感数据要加密存储
- 日志中不能包含密码等敏感信息
- 使用安全的随机数生成器
- 定期更新依赖库的安全版本

## 测试要求

### 测试覆盖
- 核心功能必须有单元测试
- 关键路径要有集成测试
- 边界条件和异常情况要测试
- 保持测试代码的简洁性

### 测试原则
- 测试要独立且可重复
- 使用有意义的测试名称
- 一个测试只验证一个行为
- 测试失败时要有清晰的错误信息

## 版本控制

### Git规范
- 提交信息要清晰描述变更
- 小步提交，避免大批量变更
- 功能开发使用分支
- 代码审查后才能合并

### 发布管理
- 使用语义化版本号
- 重要变更要有发布说明
- 保持向后兼容性
- 数据库变更要有迁移脚本
