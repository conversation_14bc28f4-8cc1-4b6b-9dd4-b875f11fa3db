# 前端组件设计文档

## 组件架构原则

### 组件分层
- **页面组件（Pages）**：路由级别的顶层组件
- **布局组件（Layouts）**：页面结构和布局
- **业务组件（Features）**：具体功能模块
- **通用组件（Components）**：可复用的基础组件

### 设计原则
- 单一职责：每个组件只负责一个功能
- 可复用性：通用组件要高度可复用
- 可组合性：复杂功能通过组件组合实现
- 可测试性：组件要易于单元测试

## 核心组件设计

### 布局组件

#### MainLayout
- **职责**：整体页面布局
- **结构**：左侧主功能区 + 右侧辅助功能区
- **Props**：
  - `showSidebar: boolean` - 是否显示右侧栏
  - `sidebarContent: ReactNode` - 右侧栏内容

#### MobileLayout
- **职责**：移动端双屏布局
- **结构**：可滑动的双屏设计
- **Props**：
  - `currentScreen: number` - 当前屏幕索引
  - `onScreenChange: (index: number) => void`

### 对话组件

#### ChatContainer
- **职责**：对话区域的容器组件
- **功能**：消息列表展示、滚动管理
- **子组件**：MessageList、MessageInput

#### MessageList
- **职责**：消息列表展示
- **功能**：虚拟滚动、历史加载、筛选
- **Props**：
  - `messages: Message[]`
  - `onLoadMore: () => void`
  - `loading: boolean`

#### MessageItem
- **职责**：单条消息展示
- **功能**：用户消息、Silicon回复、状态指示
- **Props**：
  - `message: Message`
  - `isStreaming: boolean`
  - `showTimestamp: boolean`

#### MessageInput
- **职责**：消息输入区域
- **功能**：文本输入、文件上传、发送
- **Props**：
  - `onSend: (content: string, files?: File[]) => void`
  - `disabled: boolean`
  - `placeholder: string`

### 辅助功能组件

#### SidebarPanel
- **职责**：右侧辅助功能面板容器
- **功能**：面板切换、内容展示
- **Props**：
  - `activePanel: string`
  - `onPanelChange: (panel: string) => void`

#### TaskViewer
- **职责**：任务状态和历史查看
- **功能**：任务文档展示、历史筛选
- **Props**：
  - `currentTask: Task | null`
  - `taskHistory: Task[]`
  - `onTaskSelect: (task: Task) => void`

#### SystemMonitor
- **职责**：系统资源监控
- **功能**：实时数据图表、状态指示
- **Props**：
  - `systemData: SystemStatus`
  - `updateInterval: number`

#### LogViewer
- **职责**：日志查看器
- **功能**：日志展示、类型筛选、搜索
- **Props**：
  - `logs: LogEntry[]`
  - `filters: LogFilter`
  - `onFilterChange: (filters: LogFilter) => void`

### 通用组件

#### FileUpload
- **职责**：文件上传组件
- **功能**：拖拽上传、多文件、预览
- **Props**：
  - `onUpload: (files: File[]) => void`
  - `accept: string`
  - `multiple: boolean`
  - `maxSize: number`

#### StreamingText
- **职责**：流式文本显示
- **功能**：打字机效果、流式更新
- **Props**：
  - `content: string`
  - `isStreaming: boolean`
  - `speed: number`

#### StatusIndicator
- **职责**：状态指示器
- **功能**：连接状态、加载状态、错误状态
- **Props**：
  - `status: 'connected' | 'disconnected' | 'loading' | 'error'`
  - `message?: string`

#### ThemeToggle
- **职责**：主题切换按钮
- **功能**：黑白主题切换
- **Props**：
  - `theme: 'light' | 'dark'`
  - `onToggle: (theme: string) => void`

#### LanguageSelector
- **职责**：语言选择器
- **功能**：多语言切换
- **Props**：
  - `currentLanguage: string`
  - `languages: Language[]`
  - `onLanguageChange: (lang: string) => void`

## 组件通信

### Props传递
- 父子组件通过Props传递数据
- 使用TypeScript严格定义Props类型
- 避免Props drilling，超过3层使用Context

### Context使用
- **ThemeContext**：主题状态管理
- **LanguageContext**：多语言状态
- **WebSocketContext**：WebSocket连接管理

### 自定义Hooks
- **useWebSocket**：WebSocket连接和消息处理
- **useLocalStorage**：本地存储管理
- **useDebounce**：防抖处理
- **useInfiniteScroll**：无限滚动

## 组件测试

### 测试策略
- 每个组件都要有单元测试
- 使用React Testing Library
- 测试用户交互和状态变化
- Mock外部依赖和API调用

### 测试覆盖
- 组件渲染测试
- Props变化测试
- 事件处理测试
- 边界条件测试
