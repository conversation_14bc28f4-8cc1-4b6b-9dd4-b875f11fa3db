# 前端状态管理方案

## 状态管理架构

### 状态分层
- **全局状态**：应用级别的共享状态
- **页面状态**：页面级别的状态管理
- **组件状态**：组件内部的局部状态
- **服务器状态**：来自API的远程数据

### 技术选择
- **Zustand**：轻量级全局状态管理
- **React Query**：服务器状态管理和缓存
- **useState/useReducer**：组件本地状态
- **Context API**：跨组件状态共享

## 全局状态设计

### 应用状态（AppStore）
```typescript
interface AppState {
  // 用户设置
  theme: 'light' | 'dark'
  language: string
  
  // 连接状态
  connectionStatus: 'connected' | 'disconnected' | 'connecting'
  
  // 界面状态
  sidebarVisible: boolean
  activeSidebarPanel: string
  
  // 操作方法
  setTheme: (theme: string) => void
  setLanguage: (language: string) => void
  toggleSidebar: () => void
  setActiveSidebarPanel: (panel: string) => void
}
```

### 对话状态（ChatStore）
```typescript
interface ChatState {
  // 消息数据
  messages: Message[]
  currentMessage: string
  isTyping: boolean
  
  // 历史管理
  hasMoreHistory: boolean
  loadingHistory: boolean
  
  // 筛选和搜索
  messageFilter: MessageFilter
  searchQuery: string
  
  // 操作方法
  addMessage: (message: Message) => void
  updateCurrentMessage: (content: string) => void
  loadMoreHistory: () => void
  setMessageFilter: (filter: MessageFilter) => void
}
```

### 任务状态（TaskStore）
```typescript
interface TaskState {
  // 当前任务
  currentTask: Task | null
  taskProgress: TaskProgress
  
  // 任务历史
  taskHistory: Task[]
  taskFilter: TaskFilter
  
  // 操作方法
  setCurrentTask: (task: Task) => void
  updateTaskProgress: (progress: TaskProgress) => void
  addTaskToHistory: (task: Task) => void
  setTaskFilter: (filter: TaskFilter) => void
}
```

### 系统状态（SystemStore）
```typescript
interface SystemState {
  // 系统监控
  systemStatus: SystemStatus
  resourceUsage: ResourceUsage[]
  
  // 日志管理
  logs: LogEntry[]
  logFilter: LogFilter
  
  // 操作方法
  updateSystemStatus: (status: SystemStatus) => void
  addLogEntry: (log: LogEntry) => void
  setLogFilter: (filter: LogFilter) => void
}
```

## 服务器状态管理

### React Query配置
```typescript
// 查询配置
const queryConfig = {
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5分钟
      cacheTime: 10 * 60 * 1000, // 10分钟
      refetchOnWindowFocus: false,
      retry: 3,
    },
  },
}

// 自定义Hooks
const useMessages = (filter?: MessageFilter) => {
  return useInfiniteQuery({
    queryKey: ['messages', filter],
    queryFn: ({ pageParam = 0 }) => fetchMessages(pageParam, filter),
    getNextPageParam: (lastPage) => lastPage.nextCursor,
  })
}

const useTaskHistory = (filter?: TaskFilter) => {
  return useQuery({
    queryKey: ['taskHistory', filter],
    queryFn: () => fetchTaskHistory(filter),
  })
}
```

### WebSocket状态同步
```typescript
// WebSocket消息处理
const useWebSocketSync = () => {
  const { addMessage } = useChatStore()
  const { updateSystemStatus } = useSystemStore()
  const { updateTaskProgress } = useTaskStore()
  
  useEffect(() => {
    const ws = new WebSocket(WS_URL)
    
    ws.onmessage = (event) => {
      const data = JSON.parse(event.data)
      
      switch (data.type) {
        case 'silicon_response':
          addMessage(data.message)
          break
        case 'system_status':
          updateSystemStatus(data.status)
          break
        case 'task_update':
          updateTaskProgress(data.progress)
          break
      }
    }
    
    return () => ws.close()
  }, [])
}
```

## 状态持久化

### 本地存储策略
```typescript
// 持久化配置
const persistConfig = {
  // 需要持久化的状态
  theme: true,
  language: true,
  sidebarVisible: true,
  activeSidebarPanel: true,
  
  // 不持久化的状态
  connectionStatus: false,
  messages: false, // 从服务器获取
  systemStatus: false, // 实时数据
}

// 自动持久化Hook
const usePersistentState = <T>(
  key: string,
  initialValue: T,
  shouldPersist: boolean = true
) => {
  const [state, setState] = useState<T>(() => {
    if (!shouldPersist) return initialValue
    
    const stored = localStorage.getItem(key)
    return stored ? JSON.parse(stored) : initialValue
  })
  
  useEffect(() => {
    if (shouldPersist) {
      localStorage.setItem(key, JSON.stringify(state))
    }
  }, [key, state, shouldPersist])
  
  return [state, setState] as const
}
```

## 状态更新模式

### 乐观更新
```typescript
// 发送消息时的乐观更新
const sendMessage = async (content: string) => {
  const tempMessage: Message = {
    id: generateTempId(),
    content,
    sender: 'user',
    timestamp: new Date(),
    status: 'sending'
  }
  
  // 立即添加到UI
  addMessage(tempMessage)
  
  try {
    const response = await api.sendMessage(content)
    // 更新为实际消息
    updateMessage(tempMessage.id, response)
  } catch (error) {
    // 标记为失败
    updateMessage(tempMessage.id, { status: 'failed' })
  }
}
```

### 流式更新
```typescript
// Silicon回复的流式更新
const handleStreamingResponse = (messageId: string) => {
  const message = getMessage(messageId)
  
  return (chunk: string) => {
    updateMessage(messageId, {
      ...message,
      content: message.content + chunk,
      isStreaming: true
    })
  }
}
```

## 性能优化

### 状态选择器
```typescript
// 避免不必要的重渲染
const useMessageCount = () => {
  return useChatStore(state => state.messages.length)
}

const useCurrentTask = () => {
  return useTaskStore(state => state.currentTask)
}
```

### 状态分割
```typescript
// 将大的状态对象分割为小的独立状态
const useMessageList = () => {
  return useChatStore(state => state.messages)
}

const useMessageInput = () => {
  return useChatStore(state => ({
    currentMessage: state.currentMessage,
    isTyping: state.isTyping,
    updateCurrentMessage: state.updateCurrentMessage
  }))
}
```

### 防抖和节流
```typescript
// 搜索输入防抖
const useSearchDebounce = () => {
  const [searchQuery, setSearchQuery] = useState('')
  const debouncedQuery = useDebounce(searchQuery, 300)
  
  useEffect(() => {
    if (debouncedQuery) {
      // 执行搜索
      searchMessages(debouncedQuery)
    }
  }, [debouncedQuery])
  
  return [searchQuery, setSearchQuery] as const
}
```

## 错误处理

### 错误边界
```typescript
// 状态错误处理
const useErrorHandler = () => {
  const [error, setError] = useState<Error | null>(null)
  
  const handleError = useCallback((error: Error) => {
    console.error('State error:', error)
    setError(error)
    // 可以添加错误上报
  }, [])
  
  const clearError = useCallback(() => {
    setError(null)
  }, [])
  
  return { error, handleError, clearError }
}
```

### 状态恢复
```typescript
// 连接断开时的状态恢复
const useConnectionRecovery = () => {
  const { connectionStatus } = useAppStore()
  
  useEffect(() => {
    if (connectionStatus === 'connected') {
      // 重新同步状态
      syncStateWithServer()
    }
  }, [connectionStatus])
}
```
